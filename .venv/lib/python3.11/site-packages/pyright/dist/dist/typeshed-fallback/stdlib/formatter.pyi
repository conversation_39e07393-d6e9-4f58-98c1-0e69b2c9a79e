from collections.abc import Iterable
from typing import IO, Any
from typing_extensions import TypeAlias

AS_IS: None
_FontType: TypeAlias = tuple[str, bool, bool, bool]
_StylesType: TypeAlias = tuple[Any, ...]

class NullFormatter:
    writer: <PERSON>ullWriter | None
    def __init__(self, writer: NullWriter | None = None) -> None: ...
    def end_paragraph(self, blankline: int) -> None: ...
    def add_line_break(self) -> None: ...
    def add_hor_rule(self, *args: Any, **kw: Any) -> None: ...
    def add_label_data(self, format: str, counter: int, blankline: int | None = None) -> None: ...
    def add_flowing_data(self, data: str) -> None: ...
    def add_literal_data(self, data: str) -> None: ...
    def flush_softspace(self) -> None: ...
    def push_alignment(self, align: str | None) -> None: ...
    def pop_alignment(self) -> None: ...
    def push_font(self, x: _FontType) -> None: ...
    def pop_font(self) -> None: ...
    def push_margin(self, margin: int) -> None: ...
    def pop_margin(self) -> None: ...
    def set_spacing(self, spacing: str | None) -> None: ...
    def push_style(self, *styles: _StylesType) -> None: ...
    def pop_style(self, n: int = 1) -> None: ...
    def assert_line_data(self, flag: int = 1) -> None: ...

class AbstractFormatter:
    writer: NullWriter
    align: str | None
    align_stack: list[str | None]
    font_stack: list[_FontType]
    margin_stack: list[int]
    spacing: str | None
    style_stack: Any
    nospace: int
    softspace: int
    para_end: int
    parskip: int
    hard_break: int
    have_label: int
    def __init__(self, writer: NullWriter) -> None: ...
    def end_paragraph(self, blankline: int) -> None: ...
    def add_line_break(self) -> None: ...
    def add_hor_rule(self, *args: Any, **kw: Any) -> None: ...
    def add_label_data(self, format: str, counter: int, blankline: int | None = None) -> None: ...
    def format_counter(self, format: Iterable[str], counter: int) -> str: ...
    def format_letter(self, case: str, counter: int) -> str: ...
    def format_roman(self, case: str, counter: int) -> str: ...
    def add_flowing_data(self, data: str) -> None: ...
    def add_literal_data(self, data: str) -> None: ...
    def flush_softspace(self) -> None: ...
    def push_alignment(self, align: str | None) -> None: ...
    def pop_alignment(self) -> None: ...
    def push_font(self, font: _FontType) -> None: ...
    def pop_font(self) -> None: ...
    def push_margin(self, margin: int) -> None: ...
    def pop_margin(self) -> None: ...
    def set_spacing(self, spacing: str | None) -> None: ...
    def push_style(self, *styles: _StylesType) -> None: ...
    def pop_style(self, n: int = 1) -> None: ...
    def assert_line_data(self, flag: int = 1) -> None: ...

class NullWriter:
    def flush(self) -> None: ...
    def new_alignment(self, align: str | None) -> None: ...
    def new_font(self, font: _FontType) -> None: ...
    def new_margin(self, margin: int, level: int) -> None: ...
    def new_spacing(self, spacing: str | None) -> None: ...
    def new_styles(self, styles: tuple[Any, ...]) -> None: ...
    def send_paragraph(self, blankline: int) -> None: ...
    def send_line_break(self) -> None: ...
    def send_hor_rule(self, *args: Any, **kw: Any) -> None: ...
    def send_label_data(self, data: str) -> None: ...
    def send_flowing_data(self, data: str) -> None: ...
    def send_literal_data(self, data: str) -> None: ...

class AbstractWriter(NullWriter): ...

class DumbWriter(NullWriter):
    file: IO[str]
    maxcol: int
    def __init__(self, file: IO[str] | None = None, maxcol: int = 72) -> None: ...
    def reset(self) -> None: ...

def test(file: str | None = None) -> None: ...
