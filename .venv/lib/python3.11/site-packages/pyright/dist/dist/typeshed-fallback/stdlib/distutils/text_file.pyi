from typing import IO, Literal

class TextFile:
    def __init__(
        self,
        filename: str | None = None,
        file: IO[str] | None = None,
        *,
        strip_comments: bool | Literal[0, 1] = ...,
        lstrip_ws: bool | Literal[0, 1] = ...,
        rstrip_ws: bool | Literal[0, 1] = ...,
        skip_blanks: bool | Literal[0, 1] = ...,
        join_lines: bool | Literal[0, 1] = ...,
        collapse_join: bool | Literal[0, 1] = ...,
    ) -> None: ...
    def open(self, filename: str) -> None: ...
    def close(self) -> None: ...
    def warn(self, msg: str, line: list[int] | tuple[int, int] | int | None = None) -> None: ...
    def readline(self) -> str | None: ...
    def readlines(self) -> list[str]: ...
    def unreadline(self, line: str) -> str: ...
