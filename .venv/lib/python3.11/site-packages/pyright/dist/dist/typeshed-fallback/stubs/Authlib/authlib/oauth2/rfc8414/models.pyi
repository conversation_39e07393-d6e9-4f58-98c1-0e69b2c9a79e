from _typeshed import Incomplete

class AuthorizationServerMetadata(dict[str, object]):
    REGISTRY_KEYS: Incomplete
    def validate_issuer(self) -> None: ...
    def validate_authorization_endpoint(self) -> None: ...
    def validate_token_endpoint(self) -> None: ...
    def validate_jwks_uri(self) -> None: ...
    def validate_registration_endpoint(self) -> None: ...
    def validate_scopes_supported(self) -> None: ...
    def validate_response_types_supported(self) -> None: ...
    def validate_response_modes_supported(self) -> None: ...
    def validate_grant_types_supported(self) -> None: ...
    def validate_token_endpoint_auth_methods_supported(self) -> None: ...
    def validate_token_endpoint_auth_signing_alg_values_supported(self) -> None: ...
    def validate_service_documentation(self) -> None: ...
    def validate_ui_locales_supported(self) -> None: ...
    def validate_op_policy_uri(self) -> None: ...
    def validate_op_tos_uri(self) -> None: ...
    def validate_revocation_endpoint(self) -> None: ...
    def validate_revocation_endpoint_auth_methods_supported(self) -> None: ...
    def validate_revocation_endpoint_auth_signing_alg_values_supported(self) -> None: ...
    def validate_introspection_endpoint(self) -> None: ...
    def validate_introspection_endpoint_auth_methods_supported(self) -> None: ...
    def validate_introspection_endpoint_auth_signing_alg_values_supported(self) -> None: ...
    def validate_code_challenge_methods_supported(self) -> None: ...
    @property
    def response_modes_supported(self): ...
    @property
    def grant_types_supported(self): ...
    @property
    def token_endpoint_auth_methods_supported(self): ...
    @property
    def revocation_endpoint_auth_methods_supported(self): ...
    @property
    def introspection_endpoint_auth_methods_supported(self): ...
    def validate(self) -> None: ...
    def __getattr__(self, key): ...

def validate_array_value(metadata, key) -> None: ...
