from authlib.oauth2.rfc6749 import TokenEndpoint

class IntrospectionEndpoint(TokenEndpoint):
    ENDPOINT_NAME: str
    def authenticate_token(self, request, client): ...
    def check_params(self, request, client) -> None: ...
    def create_endpoint_response(self, request): ...
    def create_introspection_payload(self, token): ...
    def check_permission(self, token, client, request) -> None: ...
    def query_token(self, token_string, token_type_hint) -> None: ...
    def introspect_token(self, token) -> None: ...
