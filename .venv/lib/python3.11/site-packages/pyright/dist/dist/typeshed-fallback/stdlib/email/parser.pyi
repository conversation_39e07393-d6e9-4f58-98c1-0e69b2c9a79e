from _typeshed import SupportsRead
from collections.abc import Callable
from email._policybase import _MessageT
from email.feedparser import <PERSON>tes<PERSON><PERSON><PERSON><PERSON><PERSON> as Bytes<PERSON><PERSON>Parser, <PERSON>ed<PERSON><PERSON><PERSON> as FeedParser
from email.message import Message
from email.policy import Policy
from io import _WrappedBuffer
from typing import Generic, overload

__all__ = ["Parse<PERSON>", "HeaderParser", "BytesParser", "BytesHeaderParser", "FeedParser", "BytesFeedParser"]

class Parser(Generic[_MessageT]):
    @overload
    def __init__(self: Parser[Message[str, str]], _class: None = None) -> None: ...
    @overload
    def __init__(self, _class: None = None, *, policy: Policy[_MessageT]) -> None: ...
    @overload
    def __init__(self, _class: Callable[[], _MessageT] | None, *, policy: Policy[_MessageT] = ...) -> None: ...
    def parse(self, fp: SupportsRead[str], headersonly: bool = False) -> _MessageT: ...
    def parsestr(self, text: str, headersonly: bool = False) -> _MessageT: ...

class HeaderParser(Parser[_MessageT]):
    def parse(self, fp: SupportsRead[str], headersonly: bool = True) -> _MessageT: ...
    def parsestr(self, text: str, headersonly: bool = True) -> _MessageT: ...

class BytesParser(Generic[_MessageT]):
    parser: Parser[_MessageT]
    @overload
    def __init__(self: BytesParser[Message[str, str]], _class: None = None) -> None: ...
    @overload
    def __init__(self, _class: None = None, *, policy: Policy[_MessageT]) -> None: ...
    @overload
    def __init__(self, _class: Callable[[], _MessageT], *, policy: Policy[_MessageT] = ...) -> None: ...
    def parse(self, fp: _WrappedBuffer, headersonly: bool = False) -> _MessageT: ...
    def parsebytes(self, text: bytes | bytearray, headersonly: bool = False) -> _MessageT: ...

class BytesHeaderParser(BytesParser[_MessageT]):
    def parse(self, fp: _WrappedBuffer, headersonly: bool = True) -> _MessageT: ...
    def parsebytes(self, text: bytes | bytearray, headersonly: bool = True) -> _MessageT: ...
